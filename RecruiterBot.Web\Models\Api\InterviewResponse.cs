using RecruiterBot.Core.Models;

namespace RecruiterBot.Web.Models.Api
{
    public class InterviewResponse
    {
        public string Id { get; set; }
        public string JobDescription { get; set; }
        public DateTime InterviewDateTimeUtc { get; set; }
        public DateTime InterviewDateTimeLocal { get; set; }
        public string TimeZone { get; set; }
        public InterviewStatus Status { get; set; }
        public string StatusDisplayName { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedDateUtc { get; set; }
        public DateTime? CompletedDateUtc { get; set; }
        public DateTime? CancelledDateUtc { get; set; }
        public string CancellationReason { get; set; }
        public bool IsUpcoming { get; set; }
        public bool IsPast { get; set; }
        
        // Candidate information
        public CandidateInfo Candidate { get; set; }
        
        // LLM Model information
        public LLMModelInfo LLMModel { get; set; }
    }

    public class CandidateInfo
    {
        public string Id { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string FullName => $"{FirstName} {LastName}";
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public List<string> Skills { get; set; } = new List<string>();
        public int? YearsOfExperience { get; set; }
        public string CurrentJobTitle { get; set; }
        public string CurrentCompany { get; set; }
    }

    public class LLMModelInfo
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Provider { get; set; }
        public string Description { get; set; }
    }

    public class InterviewListResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<InterviewResponse> Interviews { get; set; } = new List<InterviewResponse>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }
}
