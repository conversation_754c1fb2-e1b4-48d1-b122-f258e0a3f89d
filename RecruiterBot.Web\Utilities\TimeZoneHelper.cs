namespace RecruiterBot.Web.Utilities
{
    public static class TimeZoneHelper
    {
        public static DateTime ConvertFromUtc(DateTime utcDateTime, string timeZoneId)
        {
            if (string.IsNullOrEmpty(timeZoneId) || timeZoneId == "UTC")
                return utcDateTime;

            try
            {
                var timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
                return TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, timeZone);
            }
            catch (TimeZoneNotFoundException)
            {
                // Fallback to UTC if timezone is not found
                return utcDateTime;
            }
            catch (InvalidTimeZoneException)
            {
                // Fallback to UTC if timezone is invalid
                return utcDateTime;
            }
        }

        public static DateTime ConvertToUtc(DateTime localDateTime, string timeZoneId)
        {
            if (string.IsNullOrEmpty(timeZoneId) || timeZoneId == "UTC")
                return localDateTime;

            try
            {
                var timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
                return TimeZoneInfo.ConvertTimeToUtc(localDateTime, timeZone);
            }
            catch (TimeZoneNotFoundException)
            {
                // Assume UTC if timezone is not found
                return localDateTime;
            }
            catch (InvalidTimeZoneException)
            {
                // Assume UTC if timezone is invalid
                return localDateTime;
            }
        }

        public static string GetTimeZoneDisplayName(string timeZoneId)
        {
            if (string.IsNullOrEmpty(timeZoneId) || timeZoneId == "UTC")
                return "UTC";

            try
            {
                var timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
                return timeZone.DisplayName;
            }
            catch
            {
                return timeZoneId;
            }
        }
    }
}
